<script setup lang="ts">
import { computed, ref } from 'vue';

defineOptions({
  name: 'RightBottomContainer'
});

interface Props {
  title: string;
  icon: string;
  cardType?: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'default',
  animationDelay: 0
});

// 督办事项数据
interface SupervisionItem {
  id: string;
  title: string;
  level: 'urgent' | 'important' | 'normal';
  status: 'pending' | 'processing' | 'completed' | 'overdue';
  deadline: string;
  responsible: string;
  daysLeft: number;
  completionRate: number;
}

const supervisionItems = ref<SupervisionItem[]>([
  {
    id: '1',
    title: '5G基站建设进度督办',
    level: 'urgent',
    status: 'processing',
    deadline: '2024-12-20',
    responsible: '建设部',
    daysLeft: 15,
    completionRate: 75
  },
  {
    id: '2',
    title: '客户投诉处理督办',
    level: 'urgent',
    status: 'overdue',
    deadline: '2024-12-01',
    responsible: '客服部',
    daysLeft: -4,
    completionRate: 60
  },
  {
    id: '3',
    title: '网络优化项目督办',
    level: 'important',
    status: 'processing',
    deadline: '2024-12-25',
    responsible: '技术部',
    daysLeft: 20,
    completionRate: 85
  },
  {
    id: '4',
    title: '安全检查整改督办',
    level: 'important',
    status: 'completed',
    deadline: '2024-11-30',
    responsible: '安全部',
    daysLeft: 0,
    completionRate: 100
  },
  {
    id: '5',
    title: '员工培训计划督办',
    level: 'normal',
    status: 'pending',
    deadline: '2025-01-10',
    responsible: '人事部',
    daysLeft: 36,
    completionRate: 20
  }
]);

// 级别配置
const levelConfig = {
  urgent: { color: '#ff4d4f', text: '紧急', icon: 'mdi:alert-circle' },
  important: { color: '#faad14', text: '重要', icon: 'mdi:star' },
  normal: { color: '#52c41a', text: '一般', icon: 'mdi:circle-outline' }
};

// 状态配置
const statusConfig = {
  pending: { color: '#8c8c8c', text: '待处理', icon: 'mdi:clock-outline' },
  processing: { color: '#1890ff', text: '处理中', icon: 'mdi:loading' },
  completed: { color: '#52c41a', text: '已完成', icon: 'mdi:check-circle' },
  overdue: { color: '#ff4d4f', text: '已逾期', icon: 'mdi:alert-circle-outline' }
};

// 统计数据
const stats = computed(() => {
  const total = supervisionItems.value.length;
  const completed = supervisionItems.value.filter(item => item.status === 'completed').length;
  const overdue = supervisionItems.value.filter(item => item.status === 'overdue').length;
  const urgent = supervisionItems.value.filter(item => item.level === 'urgent').length;

  return {
    total,
    completed,
    overdue,
    urgent,
    completionRate: Math.round((completed / total) * 100)
  };
});
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    :card-type="props.cardType as any"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
    theme="purple"
  >
    <div class="h-full flex flex-col">
      <!-- 统计概览 -->
      <div class="mb-8px flex flex-shrink-0 items-center justify-between">
        <div class="flex items-center gap-6px">
          <SvgIcon icon="mdi:clipboard-check" class="text-16px text-purple-400" />
          <span class="text-12px text-white/90 font-500">督办概况</span>
        </div>
        <div class="flex items-center gap-4px">
          <span class="text-16px text-purple-400 font-600">{{ stats.completionRate }}%</span>
        </div>
      </div>

      <!-- 关键指标 -->
      <div class="grid grid-cols-4 mb-8px flex-shrink-0 gap-4px">
        <div class="rounded-4px bg-purple-500/20 p-4px text-center">
          <div class="text-12px text-purple-400 font-600">{{ stats.total }}</div>
          <div class="text-8px text-white/60">总数</div>
        </div>
        <div class="rounded-4px bg-red-500/20 p-4px text-center">
          <div class="text-12px text-red-400 font-600">{{ stats.overdue }}</div>
          <div class="text-8px text-white/60">逾期</div>
        </div>
        <div class="rounded-4px bg-orange-500/20 p-4px text-center">
          <div class="text-12px text-orange-400 font-600">{{ stats.urgent }}</div>
          <div class="text-8px text-white/60">紧急</div>
        </div>
        <div class="rounded-4px bg-green-500/20 p-4px text-center">
          <div class="text-12px text-green-400 font-600">{{ stats.completed }}</div>
          <div class="text-8px text-white/60">完成</div>
        </div>
      </div>

      <!-- 督办事项列表 -->
      <div class="min-h-0 flex-1 overflow-hidden">
        <div class="scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent h-full overflow-y-auto">
          <div class="space-y-6px">
            <div
              v-for="item in supervisionItems"
              :key="item.id"
              class="border rounded-4px p-6px transition-all duration-200 hover:border-opacity-60"
              :style="{
                borderColor: `${statusConfig[item.status].color}40`,
                backgroundColor: `${statusConfig[item.status].color}10`
              }"
            >
              <!-- 事项头部 -->
              <div class="mb-3px flex items-center justify-between">
                <div class="flex items-center gap-4px">
                  <SvgIcon
                    :icon="levelConfig[item.level].icon"
                    :style="{ color: levelConfig[item.level].color }"
                    class="text-10px"
                  />
                  <span class="truncate text-10px text-white/90 font-500">{{ item.title }}</span>
                </div>
                <div class="flex items-center gap-2px">
                  <div
                    class="rounded-2px px-2px py-1px text-8px font-500"
                    :style="{
                      color: levelConfig[item.level].color,
                      backgroundColor: `${levelConfig[item.level].color}20`
                    }"
                  >
                    {{ levelConfig[item.level].text }}
                  </div>
                </div>
              </div>

              <!-- 进度和状态 -->
              <div class="mb-3px flex items-center justify-between">
                <div class="mr-4px flex-1">
                  <div class="h-2px overflow-hidden rounded-full bg-gray-700">
                    <div
                      class="h-full rounded-full transition-all duration-500"
                      :style="{
                        width: `${item.completionRate}%`,
                        backgroundColor: statusConfig[item.status].color
                      }"
                    />
                  </div>
                </div>
                <span class="text-8px font-600" :style="{ color: statusConfig[item.status].color }">
                  {{ item.completionRate }}%
                </span>
              </div>

              <!-- 详细信息 -->
              <div class="flex items-center justify-between text-8px text-white/60">
                <div class="flex items-center gap-3px">
                  <SvgIcon
                    :icon="statusConfig[item.status].icon"
                    :style="{ color: statusConfig[item.status].color }"
                    class="text-8px"
                  />
                  <span>{{ statusConfig[item.status].text }}</span>
                </div>
                <div class="flex items-center gap-6px">
                  <span>{{ item.responsible }}</span>
                  <span
                    :class="
                      item.daysLeft < 0 ? 'text-red-400' : item.daysLeft < 7 ? 'text-orange-400' : 'text-white/60'
                    "
                  >
                    {{ item.daysLeft < 0 ? `逾期${Math.abs(item.daysLeft)}天` : `剩余${item.daysLeft}天` }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 全屏内容 -->
    <template #fullscreen-content>
      <div class="h-full p-24px">
        <!-- 全屏标题 -->
        <div class="mb-32px text-center">
          <div class="mb-16px flex items-center justify-center gap-12px">
            <SvgIcon icon="mdi:clipboard-check-multiple" class="text-48px text-purple-400" />
            <h2 class="text-32px text-white/90 font-600">{{ props.title }} - 详细视图</h2>
          </div>
          <div class="text-16px text-white/60">移动公司重点督办事项跟踪管理</div>
        </div>

        <div class="grid grid-cols-12 h-[calc(100%-120px)] gap-24px">
          <!-- 左侧：统计概览 -->
          <div class="col-span-4 space-y-24px">
            <!-- 总体完成率 -->
            <div class="border border-purple-400/30 rounded-12px bg-purple-400/10 p-20px">
              <div class="mb-16px flex items-center gap-12px">
                <SvgIcon icon="mdi:chart-donut" class="text-24px text-purple-400" />
                <h3 class="text-20px text-white/90 font-600">完成率</h3>
              </div>

              <div class="relative mx-auto mb-20px h-48 w-48">
                <svg class="h-48 w-48 transform -rotate-90" viewBox="0 0 36 36">
                  <path
                    class="text-gray-700"
                    stroke="currentColor"
                    stroke-width="2"
                    fill="none"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                  <path
                    class="text-purple-400"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    fill="none"
                    :stroke-dasharray="`${stats.completionRate}, 100`"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                </svg>
                <div class="absolute inset-0 flex flex-col items-center justify-center">
                  <span class="text-28px text-purple-400 font-600">{{ stats.completionRate }}%</span>
                  <span class="text-12px text-white/60">总完成率</span>
                </div>
              </div>
            </div>

            <!-- 关键指标 -->
            <div class="border border-blue-400/30 rounded-12px bg-blue-400/10 p-20px">
              <div class="mb-16px flex items-center gap-12px">
                <SvgIcon icon="mdi:chart-bar" class="text-24px text-blue-400" />
                <h3 class="text-20px text-white/90 font-600">关键指标</h3>
              </div>

              <div class="grid grid-cols-2 gap-12px">
                <div class="flex flex-col items-center rounded-8px bg-purple-500/20 p-12px">
                  <SvgIcon icon="mdi:format-list-bulleted" class="mb-8px text-20px text-purple-400" />
                  <span class="mb-4px text-14px text-white/80">督办总数</span>
                  <span class="text-18px text-purple-400 font-600">{{ stats.total }}</span>
                </div>
                <div class="flex flex-col items-center rounded-8px bg-red-500/20 p-12px">
                  <SvgIcon icon="mdi:alert-circle" class="mb-8px text-20px text-red-400" />
                  <span class="mb-4px text-14px text-white/80">逾期事项</span>
                  <span class="text-18px text-red-400 font-600">{{ stats.overdue }}</span>
                </div>
                <div class="flex flex-col items-center rounded-8px bg-orange-500/20 p-12px">
                  <SvgIcon icon="mdi:fire" class="mb-8px text-20px text-orange-400" />
                  <span class="mb-4px text-14px text-white/80">紧急事项</span>
                  <span class="text-18px text-orange-400 font-600">{{ stats.urgent }}</span>
                </div>
                <div class="flex flex-col items-center rounded-8px bg-green-500/20 p-12px">
                  <SvgIcon icon="mdi:check-circle" class="mb-8px text-20px text-green-400" />
                  <span class="mb-4px text-14px text-white/80">已完成</span>
                  <span class="text-18px text-green-400 font-600">{{ stats.completed }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：详细列表 -->
          <div class="col-span-8">
            <div class="h-full border border-gray-600/30 rounded-12px bg-gray-800/20 p-20px">
              <div class="mb-20px flex items-center gap-12px">
                <SvgIcon icon="mdi:clipboard-list" class="text-24px text-blue-400" />
                <h3 class="text-20px text-white/90 font-600">督办事项详情</h3>
              </div>

              <div
                class="scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent h-[calc(100%-60px)] overflow-y-auto"
              >
                <div class="space-y-16px">
                  <div
                    v-for="item in supervisionItems"
                    :key="item.id"
                    class="border rounded-8px p-16px transition-all duration-200 hover:border-opacity-80"
                    :style="{
                      borderColor: `${statusConfig[item.status].color}40`,
                      backgroundColor: `${statusConfig[item.status].color}10`
                    }"
                  >
                    <!-- 事项标题行 -->
                    <div class="mb-12px flex items-center justify-between">
                      <div class="flex items-center gap-12px">
                        <SvgIcon
                          :icon="levelConfig[item.level].icon"
                          :style="{ color: levelConfig[item.level].color }"
                          class="text-18px"
                        />
                        <h4 class="text-16px text-white/90 font-600">{{ item.title }}</h4>
                      </div>
                      <div class="flex items-center gap-8px">
                        <div
                          class="rounded-4px px-8px py-2px text-12px font-500"
                          :style="{
                            color: levelConfig[item.level].color,
                            backgroundColor: `${levelConfig[item.level].color}20`
                          }"
                        >
                          {{ levelConfig[item.level].text }}
                        </div>
                        <div
                          class="rounded-4px px-8px py-2px text-12px font-500"
                          :style="{
                            color: statusConfig[item.status].color,
                            backgroundColor: `${statusConfig[item.status].color}20`
                          }"
                        >
                          {{ statusConfig[item.status].text }}
                        </div>
                      </div>
                    </div>

                    <!-- 进度条 -->
                    <div class="mb-12px">
                      <div class="mb-4px flex items-center justify-between">
                        <span class="text-12px text-white/60">完成进度</span>
                        <span class="text-12px font-600" :style="{ color: statusConfig[item.status].color }">
                          {{ item.completionRate }}%
                        </span>
                      </div>
                      <div class="h-8px overflow-hidden rounded-full bg-gray-700">
                        <div
                          class="h-full rounded-full transition-all duration-1000"
                          :style="{
                            width: `${item.completionRate}%`,
                            backgroundColor: statusConfig[item.status].color
                          }"
                        />
                      </div>
                    </div>

                    <!-- 详细信息 -->
                    <div class="grid grid-cols-3 gap-16px text-14px">
                      <div class="flex items-center gap-8px">
                        <SvgIcon icon="mdi:account-group" class="text-16px text-blue-400" />
                        <span class="text-white/60">负责部门:</span>
                        <span class="text-white/90">{{ item.responsible }}</span>
                      </div>
                      <div class="flex items-center gap-8px">
                        <SvgIcon icon="mdi:calendar-clock" class="text-16px text-green-400" />
                        <span class="text-white/60">截止时间:</span>
                        <span class="text-white/90">{{ item.deadline }}</span>
                      </div>
                      <div class="flex items-center gap-8px">
                        <SvgIcon icon="mdi:timer-sand" class="text-16px text-orange-400" />
                        <span class="text-white/60">剩余时间:</span>
                        <span
                          :class="
                            item.daysLeft < 0 ? 'text-red-400' : item.daysLeft < 7 ? 'text-orange-400' : 'text-white/90'
                          "
                        >
                          {{ item.daysLeft < 0 ? `逾期${Math.abs(item.daysLeft)}天` : `${item.daysLeft}天` }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </DashboardCard>
</template>
