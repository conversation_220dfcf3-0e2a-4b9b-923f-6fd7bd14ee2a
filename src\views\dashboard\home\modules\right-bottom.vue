<script setup lang="ts">
import { computed, ref } from 'vue';
import { useEcharts } from '@/hooks/common/echarts';

defineOptions({
  name: 'RightBottomContainer'
});

interface Props {
  title: string;
  icon: string;
  cardType?: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'default',
  animationDelay: 0
});

// 督办事项数据
const supervisionData = ref([
  { name: '5G基站建设', value: 75, status: 'processing' as const },
  { name: '客户投诉处理', value: 60, status: 'overdue' as const },
  { name: '网络优化', value: 85, status: 'processing' as const },
  { name: '安全检查', value: 100, status: 'completed' as const },
  { name: '员工培训', value: 20, status: 'pending' as const }
]);

// 状态配置
const statusConfig = {
  pending: { color: '#8c8c8c', text: '待处理' },
  processing: { color: '#1890ff', text: '处理中' },
  completed: { color: '#52c41a', text: '已完成' },
  overdue: { color: '#ff4d4f', text: '已逾期' }
} as const;

// 统计数据
const stats = computed(() => {
  const total = supervisionData.value.length;
  const completed = supervisionData.value.filter(item => item.status === 'completed').length;
  const overdue = supervisionData.value.filter(item => item.status === 'overdue').length;
  const processing = supervisionData.value.filter(item => item.status === 'processing').length;

  return {
    total,
    completed,
    overdue,
    processing,
    completionRate: Math.round((completed / total) * 100)
  };
});

// 创建饼图配置
const createPieChart = () => ({
  backgroundColor: 'transparent',
  legend: {
    orient: 'vertical',
    left: 'left',
    top: 'center',
    textStyle: {
      color: '#ffffff80',
      fontSize: 10
    }
  },
  series: [
    {
      name: '督办状态',
      type: 'pie' as const,
      radius: ['40%', '70%'],
      center: ['65%', '50%'],
      data: [
        { value: stats.value.completed, name: '已完成', itemStyle: { color: statusConfig.completed.color } },
        { value: stats.value.processing, name: '处理中', itemStyle: { color: statusConfig.processing.color } },
        { value: stats.value.overdue, name: '已逾期', itemStyle: { color: statusConfig.overdue.color } },
        {
          value: supervisionData.value.filter(item => item.status === 'pending').length,
          name: '待处理',
          itemStyle: { color: statusConfig.pending.color }
        }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        show: true,
        formatter: '{b}: {c}',
        fontSize: 10,
        color: '#ffffff'
      }
    }
  ]
});

// 创建进度条图配置
const createProgressChart = () => ({
  backgroundColor: 'transparent',
  grid: {
    left: '5%',
    right: '5%',
    top: '10%',
    bottom: '15%',
    containLabel: true
  },
  xAxis: {
    type: 'value' as const,
    max: 100,
    axisLine: { show: false },
    axisTick: { show: false },
    axisLabel: {
      color: '#ffffff60',
      fontSize: 10,
      formatter: '{value}%'
    },
    splitLine: {
      lineStyle: {
        color: '#ffffff20',
        type: 'dashed' as const
      }
    }
  },
  yAxis: {
    type: 'category' as const,
    data: supervisionData.value.map(item => item.name),
    axisLine: {
      lineStyle: { color: '#ffffff40' }
    },
    axisLabel: {
      color: '#ffffff80',
      fontSize: 10
    },
    axisTick: { show: false }
  },
  series: [
    {
      type: 'bar' as const,
      data: supervisionData.value.map(item => ({
        value: item.value,
        itemStyle: {
          color: statusConfig[item.status].color
        }
      })),
      barHeight: '60%',
      label: {
        show: true,
        position: 'right' as const,
        color: '#ffffff',
        fontSize: 10,
        formatter: '{c}%'
      }
    }
  ]
});

// 初始化图表
const { domRef: pieRef } = useEcharts(createPieChart);
const { domRef: progressRef } = useEcharts(createProgressChart);
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    :card-type="props.cardType as any"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
    theme="purple"
  >
    <div class="h-full flex flex-col">
      <!-- 状态分布饼图 -->
      <div class="min-h-0 flex-1">
        <div ref="pieRef" class="h-full w-full" />
      </div>

      <!-- 进度条图 -->
      <div class="min-h-0 flex-1">
        <div ref="progressRef" class="h-full w-full" />
      </div>
    </div>

    <!-- 全屏内容 -->
    <template #fullscreen-content>
      <div class="h-full p-24px">
        <!-- 全屏标题 -->
        <div class="mb-32px text-center">
          <div class="mb-16px flex items-center justify-center gap-12px">
            <SvgIcon icon="mdi:clipboard-check-multiple" class="text-48px text-purple-400" />
            <h2 class="text-32px text-white/90 font-600">{{ props.title }} - 详细视图</h2>
          </div>
          <div class="text-16px text-white/60">移动公司重点督办事项跟踪管理</div>
        </div>

        <div class="grid grid-cols-2 h-[calc(100%-120px)] gap-24px">
          <!-- 左侧：状态分布饼图 -->
          <div class="flex flex-col">
            <h3 class="mb-16px text-20px text-white/90 font-600">状态分布</h3>
            <div class="flex-1">
              <div ref="pieRef" class="h-full w-full" />
            </div>
          </div>

          <!-- 右侧：进度条图 -->
          <div class="flex flex-col">
            <h3 class="mb-16px text-20px text-white/90 font-600">各项目进度</h3>
            <div class="flex-1">
              <div ref="progressRef" class="h-full w-full" />
            </div>
          </div>
        </div>
      </div>
    </template>
  </DashboardCard>
</template>
