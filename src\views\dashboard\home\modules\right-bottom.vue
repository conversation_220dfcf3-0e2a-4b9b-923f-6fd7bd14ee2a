<script setup lang="ts">
import { type ECOption, useEcharts } from '@/hooks/common/echarts';

defineOptions({
  name: 'RightBottomContainer'
});

interface Props {
  title: string;
  icon: string;
  cardType?: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'metric',
  animationDelay: 0
});

// 督办事项数据
const supervisionData = [
  { name: '5G基站建设', value: 75, status: '处理中' },
  { name: '客户投诉处理', value: 60, status: '已逾期' },
  { name: '网络优化', value: 85, status: '处理中' },
  { name: '安全检查', value: 100, status: '已完成' },
  { name: '员工培训', value: 20, status: '待处理' }
];

// 状态颜色配置
const statusColors = {
  待处理: '#8c8c8c',
  处理中: '#1890ff',
  已完成: '#52c41a',
  已逾期: '#ff4d4f'
};

// ECharts 配置
function createChartOptions(): ECOption {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(100, 181, 246, 0.1)'
        }
      },
      backgroundColor: 'rgba(15, 23, 42, 0.95)',
      borderColor: '#8b5cf6',
      borderWidth: 1,
      textStyle: {
        color: '#e2e8f0',
        fontSize: 12
      },
      padding: [8, 12],
      extraCssText: 'border-radius: 6px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);',
      formatter: (params: any) => {
        const data = params[0];
        return `${data.name}<br/>完成进度: ${data.value}%<br/>状态: ${supervisionData[data.dataIndex].status}`;
      }
    },
    grid: {
      left: '5%',
      right: '8%',
      top: '10%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      max: 100,
      axisLabel: {
        color: '#94a3b8',
        fontSize: 10,
        formatter: '{value}%'
      },
      axisLine: {
        lineStyle: {
          color: '#334155',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(148, 163, 184, 0.15)',
          width: 1,
          type: 'dashed'
        }
      }
    },
    yAxis: {
      type: 'category',
      data: supervisionData.map(item => item.name),
      axisLabel: {
        color: '#94a3b8',
        fontSize: 10
      },
      axisLine: {
        lineStyle: {
          color: '#334155',
          width: 1
        }
      },
      axisTick: {
        show: false
      }
    },
    series: [
      {
        type: 'bar',
        data: supervisionData.map(item => ({
          value: item.value,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 0,
              colorStops: [
                { offset: 0, color: statusColors[item.status] },
                { offset: 1, color: `${statusColors[item.status]}CC` }
              ]
            },
            borderRadius: [0, 4, 4, 0]
          }
        })),
        barHeight: '60%',
        label: {
          show: true,
          position: 'right',
          color: '#ffffff',
          fontSize: 11,
          formatter: '{c}%',
          fontWeight: 'bold'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(139, 92, 246, 0.3)'
          }
        }
      }
    ],
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut',
    animationDelay: (idx: number) => idx * 100
  };
}

// 使用 ECharts Hook
const { domRef } = useEcharts(createChartOptions);
const { domRef: fullscreenDomRef } = useEcharts(createChartOptions);
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    :card-type="props.cardType as any"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
    theme="purple"
  >
    <!-- ECharts 图表容器 -->
    <div class="relative h-full w-full overflow-hidden">
      <div ref="domRef" class="h-full min-h-0 w-full"></div>
    </div>

    <!-- 全屏内容 -->
    <template #fullscreen-content>
      <div class="h-full p-24px">
        <!-- 全屏标题 -->
        <div class="absolute left-0 right-0 top-4 z-10 text-center">
          <div class="text-2xl text-white/90 font-semibold">{{ props.title }} - 督办进度分析</div>
        </div>

        <!-- 全屏模式下的图表容器 -->
        <div class="h-full w-full pt-16">
          <div ref="fullscreenDomRef" class="h-full min-h-0 w-full"></div>
        </div>
      </div>
    </template>
  </DashboardCard>
</template>
