<script setup lang="ts">
import { computed, ref } from 'vue';

defineOptions({
  name: 'RightMiddleContainer'
});

interface Props {
  title: string;
  icon: string;
  cardType?: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'default',
  animationDelay: 0
});

// 攻坚行动数据
interface ActionItem {
  id: string;
  name: string;
  progress: number;
  status: 'completed' | 'in-progress' | 'pending' | 'urgent';
  deadline: string;
  responsible: string;
  priority: 'high' | 'medium' | 'low';
}

const actionItems = ref<ActionItem[]>([
  {
    id: '1',
    name: '5G网络覆盖优化',
    progress: 85,
    status: 'in-progress',
    deadline: '2024-12-31',
    responsible: '网络部',
    priority: 'high'
  },
  {
    id: '2',
    name: '客户投诉处理提升',
    progress: 92,
    status: 'completed',
    deadline: '2024-11-30',
    responsible: '客服部',
    priority: 'high'
  },
  {
    id: '3',
    name: '基站建设加速',
    progress: 68,
    status: 'in-progress',
    deadline: '2025-01-15',
    responsible: '建设部',
    priority: 'medium'
  },
  {
    id: '4',
    name: '用户体验优化',
    progress: 45,
    status: 'urgent',
    deadline: '2024-12-15',
    responsible: '产品部',
    priority: 'high'
  }
]);

// 状态配置
const statusConfig = {
  completed: { color: '#52c41a', text: '已完成', icon: 'mdi:check-circle' },
  'in-progress': { color: '#1890ff', text: '进行中', icon: 'mdi:clock-outline' },
  pending: { color: '#faad14', text: '待开始', icon: 'mdi:pause-circle' },
  urgent: { color: '#ff4d4f', text: '紧急', icon: 'mdi:alert-circle' }
};

// 优先级配置
const priorityConfig = {
  high: { color: '#ff4d4f', text: '高' },
  medium: { color: '#faad14', text: '中' },
  low: { color: '#52c41a', text: '低' }
};

// 计算总体进度
const overallProgress = computed(() => {
  const total = actionItems.value.reduce((sum, item) => sum + item.progress, 0);
  return Math.round(total / actionItems.value.length);
});

// 计算各状态数量
const statusStats = computed(() => {
  const stats = {
    completed: 0,
    'in-progress': 0,
    pending: 0,
    urgent: 0
  };
  actionItems.value.forEach(item => {
    stats[item.status]++;
  });
  return stats;
});
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    :card-type="props.cardType as any"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
    theme="orange"
  >
    <div class="h-full flex flex-col">
      <!-- 总体进度概览 -->
      <div class="mb-8px flex flex-shrink-0 items-center justify-between">
        <div class="flex items-center gap-6px">
          <SvgIcon icon="mdi:target" class="text-16px text-orange-400" />
          <span class="text-12px text-white/90 font-500">总体进度</span>
        </div>
        <div class="flex items-center gap-4px">
          <span class="text-16px text-orange-400 font-600">{{ overallProgress }}%</span>
        </div>
      </div>

      <!-- 进度条 -->
      <div class="mb-8px flex-shrink-0">
        <div class="h-4px overflow-hidden rounded-full bg-gray-700">
          <div
            class="h-full rounded-full from-orange-500 to-orange-400 bg-gradient-to-r transition-all duration-1000"
            :style="{ width: `${overallProgress}%` }"
          />
        </div>
      </div>

      <!-- 状态统计 -->
      <div class="grid grid-cols-2 mb-8px flex-shrink-0 gap-6px">
        <div
          v-for="(count, status) in statusStats"
          :key="status"
          class="flex items-center gap-4px rounded-4px p-6px"
          :style="{ backgroundColor: `${statusConfig[status].color}20` }"
        >
          <SvgIcon :icon="statusConfig[status].icon" :style="{ color: statusConfig[status].color }" class="text-12px" />
          <span class="text-10px text-white/80">{{ statusConfig[status].text }}</span>
          <span class="text-10px text-white font-600">{{ count }}</span>
        </div>
      </div>

      <!-- 行动项目列表 -->
      <div class="min-h-0 flex-1 overflow-hidden">
        <div class="scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent h-full overflow-y-auto">
          <div class="space-y-6px">
            <div
              v-for="item in actionItems"
              :key="item.id"
              class="border rounded-4px p-6px transition-all duration-200 hover:border-opacity-60"
              :style="{
                borderColor: `${statusConfig[item.status].color}40`,
                backgroundColor: `${statusConfig[item.status].color}10`
              }"
            >
              <!-- 项目头部 -->
              <div class="mb-4px flex items-center justify-between">
                <div class="flex items-center gap-4px">
                  <SvgIcon
                    :icon="statusConfig[item.status].icon"
                    :style="{ color: statusConfig[item.status].color }"
                    class="text-10px"
                  />
                  <span class="truncate text-10px text-white/90 font-500">{{ item.name }}</span>
                </div>
                <div
                  class="rounded-2px px-3px py-1px text-8px font-500"
                  :style="{
                    color: priorityConfig[item.priority].color,
                    backgroundColor: `${priorityConfig[item.priority].color}20`
                  }"
                >
                  {{ priorityConfig[item.priority].text }}
                </div>
              </div>

              <!-- 进度条 -->
              <div class="mb-3px">
                <div class="h-2px overflow-hidden rounded-full bg-gray-700">
                  <div
                    class="h-full rounded-full transition-all duration-500"
                    :style="{
                      width: `${item.progress}%`,
                      backgroundColor: statusConfig[item.status].color
                    }"
                  />
                </div>
              </div>

              <!-- 项目信息 -->
              <div class="flex items-center justify-between text-8px text-white/60">
                <span>{{ item.responsible }}</span>
                <span>{{ item.progress }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 全屏内容 -->
    <template #fullscreen-content>
      <div class="h-full p-24px">
        <!-- 全屏标题 -->
        <div class="mb-32px text-center">
          <div class="mb-16px flex items-center justify-center gap-12px">
            <SvgIcon icon="mdi:rocket-launch" class="text-48px text-orange-400" />
            <h2 class="text-32px text-white/90 font-600">{{ props.title }} - 详细视图</h2>
          </div>
          <div class="text-16px text-white/60">移动公司重点攻坚行动进展监控</div>
        </div>

        <div class="grid grid-cols-12 h-[calc(100%-120px)] gap-24px">
          <!-- 左侧：总体概览 -->
          <div class="col-span-4 space-y-24px">
            <!-- 总体进度 -->
            <div class="border border-orange-400/30 rounded-12px bg-orange-400/10 p-20px">
              <div class="mb-16px flex items-center gap-12px">
                <SvgIcon icon="mdi:target" class="text-24px text-orange-400" />
                <h3 class="text-20px text-white/90 font-600">总体进度</h3>
              </div>

              <div class="relative mx-auto mb-20px h-48 w-48">
                <svg class="h-48 w-48 transform -rotate-90" viewBox="0 0 36 36">
                  <path
                    class="text-gray-700"
                    stroke="currentColor"
                    stroke-width="2"
                    fill="none"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                  <path
                    class="text-orange-400"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    fill="none"
                    :stroke-dasharray="`${overallProgress}, 100`"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                </svg>
                <div class="absolute inset-0 flex flex-col items-center justify-center">
                  <span class="text-28px text-orange-400 font-600">{{ overallProgress }}%</span>
                  <span class="text-12px text-white/60">完成度</span>
                </div>
              </div>
            </div>

            <!-- 状态统计 -->
            <div class="border border-blue-400/30 rounded-12px bg-blue-400/10 p-20px">
              <div class="mb-16px flex items-center gap-12px">
                <SvgIcon icon="mdi:chart-donut" class="text-24px text-blue-400" />
                <h3 class="text-20px text-white/90 font-600">状态分布</h3>
              </div>

              <div class="grid grid-cols-2 gap-12px">
                <div
                  v-for="(count, status) in statusStats"
                  :key="status"
                  class="flex flex-col items-center rounded-8px p-12px"
                  :style="{ backgroundColor: `${statusConfig[status].color}20` }"
                >
                  <SvgIcon
                    :icon="statusConfig[status].icon"
                    :style="{ color: statusConfig[status].color }"
                    class="mb-8px text-20px"
                  />
                  <span class="mb-4px text-14px text-white/80">{{ statusConfig[status].text }}</span>
                  <span class="text-18px font-600" :style="{ color: statusConfig[status].color }">{{ count }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：详细列表 -->
          <div class="col-span-8">
            <div class="h-full border border-gray-600/30 rounded-12px bg-gray-800/20 p-20px">
              <div class="mb-20px flex items-center gap-12px">
                <SvgIcon icon="mdi:format-list-bulleted" class="text-24px text-blue-400" />
                <h3 class="text-20px text-white/90 font-600">攻坚行动详情</h3>
              </div>

              <div
                class="scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent h-[calc(100%-60px)] overflow-y-auto"
              >
                <div class="space-y-16px">
                  <div
                    v-for="item in actionItems"
                    :key="item.id"
                    class="border rounded-8px p-16px transition-all duration-200 hover:border-opacity-80"
                    :style="{
                      borderColor: `${statusConfig[item.status].color}40`,
                      backgroundColor: `${statusConfig[item.status].color}10`
                    }"
                  >
                    <!-- 项目标题行 -->
                    <div class="mb-12px flex items-center justify-between">
                      <div class="flex items-center gap-12px">
                        <SvgIcon
                          :icon="statusConfig[item.status].icon"
                          :style="{ color: statusConfig[item.status].color }"
                          class="text-18px"
                        />
                        <h4 class="text-16px text-white/90 font-600">{{ item.name }}</h4>
                      </div>
                      <div class="flex items-center gap-8px">
                        <div
                          class="rounded-4px px-8px py-2px text-12px font-500"
                          :style="{
                            color: priorityConfig[item.priority].color,
                            backgroundColor: `${priorityConfig[item.priority].color}20`
                          }"
                        >
                          {{ priorityConfig[item.priority].text }}优先级
                        </div>
                        <span class="text-16px font-600" :style="{ color: statusConfig[item.status].color }">
                          {{ item.progress }}%
                        </span>
                      </div>
                    </div>

                    <!-- 进度条 -->
                    <div class="mb-12px">
                      <div class="h-8px overflow-hidden rounded-full bg-gray-700">
                        <div
                          class="h-full rounded-full transition-all duration-1000"
                          :style="{
                            width: `${item.progress}%`,
                            backgroundColor: statusConfig[item.status].color
                          }"
                        />
                      </div>
                    </div>

                    <!-- 项目详情 -->
                    <div class="grid grid-cols-3 gap-16px text-14px">
                      <div class="flex items-center gap-8px">
                        <SvgIcon icon="mdi:account-group" class="text-16px text-blue-400" />
                        <span class="text-white/60">负责部门:</span>
                        <span class="text-white/90">{{ item.responsible }}</span>
                      </div>
                      <div class="flex items-center gap-8px">
                        <SvgIcon icon="mdi:calendar-clock" class="text-16px text-green-400" />
                        <span class="text-white/60">截止时间:</span>
                        <span class="text-white/90">{{ item.deadline }}</span>
                      </div>
                      <div class="flex items-center gap-8px">
                        <SvgIcon
                          :icon="statusConfig[item.status].icon"
                          :style="{ color: statusConfig[item.status].color }"
                          class="text-16px"
                        />
                        <span class="text-white/60">状态:</span>
                        <span :style="{ color: statusConfig[item.status].color }">
                          {{ statusConfig[item.status].text }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </DashboardCard>
</template>
