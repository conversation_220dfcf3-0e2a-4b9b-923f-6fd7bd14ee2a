<script setup lang="ts">
import { computed, ref } from 'vue';
import { useEcharts } from '@/hooks/common/echarts';

defineOptions({
  name: 'RightMiddleContainer'
});

interface Props {
  title: string;
  icon: string;
  cardType?: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'default',
  animationDelay: 0
});

// 攻坚行动数据
const actionData = ref([
  { name: '5G网络优化', value: 85, status: 'in-progress' as const },
  { name: '客户投诉处理', value: 92, status: 'completed' as const },
  { name: '基站建设', value: 68, status: 'in-progress' as const },
  { name: '用户体验', value: 45, status: 'urgent' as const }
]);

// 状态配置
const statusConfig = {
  completed: { color: '#52c41a', text: '已完成' },
  'in-progress': { color: '#1890ff', text: '进行中' },
  urgent: { color: '#ff4d4f', text: '紧急' }
} as const;

// 计算总体进度
const overallProgress = computed(() => {
  const total = actionData.value.reduce((sum, item) => sum + item.value, 0);
  return Math.round(total / actionData.value.length);
});

// 创建环形进度图配置
const createProgressChart = () => ({
  backgroundColor: 'transparent',
  series: [
    {
      name: '总体进度',
      type: 'pie' as const,
      radius: ['60%', '80%'],
      center: ['50%', '50%'],
      startAngle: 90,
      data: [
        {
          value: overallProgress.value,
          name: '已完成',
          itemStyle: {
            color: {
              type: 'linear' as const,
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#fa8c16' },
                { offset: 1, color: '#ffa940' }
              ]
            }
          }
        },
        {
          value: 100 - overallProgress.value,
          name: '未完成',
          itemStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          },
          label: { show: false },
          labelLine: { show: false }
        }
      ],
      label: {
        show: true,
        position: 'center' as const,
        formatter: `{a|${overallProgress.value}%}\n{b|总体进度}`,
        rich: {
          a: {
            fontSize: 24,
            fontWeight: 'bold',
            color: '#fa8c16'
          },
          b: {
            fontSize: 12,
            color: '#ffffff80',
            padding: [8, 0, 0, 0]
          }
        }
      },
      emphasis: {
        scale: false
      }
    }
  ]
});

// 创建柱状图配置
const createBarChart = () => ({
  backgroundColor: 'transparent',
  grid: {
    left: '5%',
    right: '5%',
    top: '10%',
    bottom: '15%',
    containLabel: true
  },
  xAxis: {
    type: 'category' as const,
    data: actionData.value.map(item => item.name),
    axisLine: {
      lineStyle: { color: '#ffffff40' }
    },
    axisLabel: {
      color: '#ffffff80',
      fontSize: 10,
      interval: 0,
      rotate: 0
    },
    axisTick: { show: false }
  },
  yAxis: {
    type: 'value' as const,
    max: 100,
    axisLine: { show: false },
    axisTick: { show: false },
    axisLabel: {
      color: '#ffffff60',
      fontSize: 10
    },
    splitLine: {
      lineStyle: {
        color: '#ffffff20',
        type: 'dashed' as const
      }
    }
  },
  series: [
    {
      type: 'bar' as const,
      data: actionData.value.map(item => ({
        value: item.value,
        itemStyle: {
          color: statusConfig[item.status].color
        }
      })),
      barWidth: '60%',
      label: {
        show: true,
        position: 'top' as const,
        color: '#ffffff',
        fontSize: 10,
        formatter: '{c}%'
      }
    }
  ]
});

// 初始化图表
const { domRef: progressRef } = useEcharts(createProgressChart);
const { domRef: barRef } = useEcharts(createBarChart);
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    :card-type="props.cardType as any"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
    theme="orange"
  >
    <div class="h-full flex flex-col">
      <!-- 总体进度环形图 -->
      <div class="flex-1 min-h-0">
        <div ref="progressRef" class="h-full w-full" />
      </div>

      <!-- 项目进度柱状图 -->
      <div class="flex-1 min-h-0">
        <div ref="barRef" class="h-full w-full" />
      </div>
    </div>

    <!-- 全屏内容 -->
    <template #fullscreen-content>
      <div class="h-full p-24px">
        <!-- 全屏标题 -->
        <div class="mb-32px text-center">
          <div class="mb-16px flex items-center justify-center gap-12px">
            <SvgIcon icon="mdi:rocket-launch" class="text-48px text-orange-400" />
            <h2 class="text-32px text-white/90 font-600">{{ props.title }} - 详细视图</h2>
          </div>
          <div class="text-16px text-white/60">移动公司重点攻坚行动进展监控</div>
        </div>

        <div class="grid grid-cols-2 h-[calc(100%-120px)] gap-24px">
          <!-- 左侧：总体进度环形图 -->
          <div class="flex flex-col">
            <h3 class="mb-16px text-20px text-white/90 font-600">总体进度</h3>
            <div class="flex-1">
              <div ref="progressRef" class="h-full w-full" />
            </div>
          </div>

          <!-- 右侧：项目进度柱状图 -->
          <div class="flex flex-col">
            <h3 class="mb-16px text-20px text-white/90 font-600">各项目进度</h3>
            <div class="flex-1">
              <div ref="barRef" class="h-full w-full" />
            </div>
          </div>
        </div>
      </div>
    </template>
  </DashboardCard>
</template>
