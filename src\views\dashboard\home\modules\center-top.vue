<script setup lang="ts">
import { computed, markRaw, onMounted, ref, shallowRef } from 'vue';
import { NButton } from 'naive-ui';
import * as echarts from 'echarts/core';
import { useEcharts } from '@/hooks/common/echarts';
import changShaMapData from '@/assets/dashboard/maps/cs.json';

defineOptions({
  name: 'CenterTopContainer'
});

interface Props {
  title: string;
  icon: string;
  cardType?: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'chart',
  animationDelay: 0
});

// 地图配置 - 使用 markRaw 避免响应式开销
const MAP_CONFIG = markRaw({
  name: 'changsha',
  zoom: 1.2,
  center: [112.98, 28.19]
} as const);

// 网络指标数据类型
interface NetworkMetrics {
  name: string;
  coordinates: [number, number]; // [经度, 纬度]
  networkScore: number; // 网络综合评分 0-100
  satisfaction: number; // 满意度 0-100
  status: 'excellent' | 'good' | 'warning' | 'poor';
  metrics: {
    coverage: number; // 覆盖率 %
    speed: number; // 平均速度 Mbps
    latency: number; // 延迟 ms
    stability: number; // 稳定性 %
    baseStations: number; // 基站数量
    users: number; // 用户数 万
  };
}

// 模拟各区县网络指标数据 - 使用 shallowRef 提高性能
const districtData = shallowRef<NetworkMetrics[]>([
  {
    name: '芙蓉区',
    coordinates: [113.049, 28.198],
    networkScore: 95,
    satisfaction: 92,
    status: 'excellent',
    metrics: { coverage: 98.5, speed: 156.8, latency: 12, stability: 99.2, baseStations: 128, users: 58.2 }
  },
  {
    name: '天心区',
    coordinates: [112.95, 28.03],
    networkScore: 88,
    satisfaction: 85,
    status: 'good',
    metrics: { coverage: 96.8, speed: 142.3, latency: 15, stability: 97.8, baseStations: 115, users: 68.5 }
  },
  {
    name: '岳麓区',
    coordinates: [112.78, 28.08],
    networkScore: 92,
    satisfaction: 89,
    status: 'excellent',
    metrics: { coverage: 97.9, speed: 168.5, latency: 11, stability: 98.6, baseStations: 156, users: 89.3 }
  },
  {
    name: '开福区',
    coordinates: [112.98, 28.33],
    networkScore: 78,
    satisfaction: 72,
    status: 'warning',
    metrics: { coverage: 94.2, speed: 125.6, latency: 22, stability: 95.1, baseStations: 98, users: 62.1 }
  },
  {
    name: '雨花区',
    coordinates: [113.11, 28.01],
    networkScore: 85,
    satisfaction: 81,
    status: 'good',
    metrics: { coverage: 96.5, speed: 138.9, latency: 16, stability: 96.8, baseStations: 132, users: 75.8 }
  },
  {
    name: '望城区',
    coordinates: [112.82, 28.36],
    networkScore: 72,
    satisfaction: 68,
    status: 'warning',
    metrics: { coverage: 92.8, speed: 118.2, latency: 28, stability: 93.5, baseStations: 76, users: 58.9 }
  },
  {
    name: '长沙县',
    coordinates: [113.17, 28.35],
    networkScore: 89,
    satisfaction: 86,
    status: 'good',
    metrics: { coverage: 97.2, speed: 152.4, latency: 14, stability: 97.9, baseStations: 145, users: 106.2 }
  },
  {
    name: '浏阳市',
    coordinates: [113.63, 28.14],
    networkScore: 65,
    satisfaction: 58,
    status: 'poor',
    metrics: { coverage: 89.5, speed: 98.7, latency: 35, stability: 89.2, baseStations: 89, users: 147.3 }
  },
  {
    name: '宁乡市',
    coordinates: [112.41, 28.15],
    networkScore: 81,
    satisfaction: 78,
    status: 'good',
    metrics: { coverage: 95.1, speed: 132.8, latency: 19, stability: 95.8, baseStations: 102, users: 119.8 }
  }
]);

// 当前选中的区域
const selectedDistrict = ref<string>('');

// 状态颜色映射 - 蓝色主题
const STATUS_COLORS = markRaw({
  excellent: {
    area: 'rgba(82, 196, 26, 0.15)',
    border: '#52c41a',
    text: '#52c41a'
  },
  good: {
    area: 'rgba(24, 144, 255, 0.15)',
    border: '#1890ff',
    text: '#1890ff'
  },
  warning: {
    area: 'rgba(250, 173, 20, 0.15)',
    border: '#faad14',
    text: '#faad14'
  },
  poor: {
    area: 'rgba(255, 77, 79, 0.15)',
    border: '#ff4d4f',
    text: '#ff4d4f'
  }
} as const);

// 获取状态颜色
const getStatusColor = (status: NetworkMetrics['status'], type: 'area' | 'border' | 'text') => {
  return STATUS_COLORS[status][type];
};

// 统计数据
const statistics = computed(() => {
  const total = districtData.value.length;
  const excellent = districtData.value.filter(d => d.status === 'excellent').length;
  const good = districtData.value.filter(d => d.status === 'good').length;
  const warning = districtData.value.filter(d => d.status === 'warning').length;
  const poor = districtData.value.filter(d => d.status === 'poor').length;
  const avgNetworkScore = Math.round(districtData.value.reduce((sum, d) => sum + d.networkScore, 0) / total);
  const avgSatisfaction = Math.round(districtData.value.reduce((sum, d) => sum + d.satisfaction, 0) / total);

  return { total, excellent, good, warning, poor, avgNetworkScore, avgSatisfaction };
});

// 注册长沙地图
onMounted(() => {
  echarts.registerMap(MAP_CONFIG.name, changShaMapData as any);
});

// 地图配置 - 蓝色主题数据大屏风格
const { domRef } = useEcharts(() => ({
  backgroundColor: 'transparent',
  tooltip: {
    show: true,
    trigger: 'item',
    backgroundColor: 'rgba(13, 41, 84, 0.95)',
    borderColor: '#1890ff',
    borderWidth: 1,
    textStyle: {
      color: '#ffffff',
      fontSize: 12
    },
    formatter: (params: any) => {
      if (params.seriesType !== 'map') return '';

      const networkData = districtData.value.find(d => d.name === params.name);
      if (!networkData) return '';

      const statusColor =
        networkData.status === 'excellent'
          ? '#52c41a'
          : networkData.status === 'good'
            ? '#1890ff'
            : networkData.status === 'warning'
              ? '#faad14'
              : '#ff4d4f';

      return `
        <div style="padding: 12px; min-width: 200px;">
          <div style="font-size: 16px; font-weight: bold; color: #40a9ff; margin-bottom: 8px; border-bottom: 1px solid rgba(24, 144, 255, 0.3); padding-bottom: 6px;">
            ${params.name}
          </div>
          <div style="margin-bottom: 6px;">
            <span style="color: #91d5ff;">网络评分:</span>
            <span style="color: ${statusColor}; font-weight: bold; margin-left: 8px;">${networkData.networkScore}</span>
          </div>
          <div style="margin-bottom: 6px;">
            <span style="color: #91d5ff;">满意度:</span>
            <span style="color: #40a9ff; font-weight: bold; margin-left: 8px;">${networkData.satisfaction}%</span>
          </div>
          <div style="margin-bottom: 6px;">
            <span style="color: #91d5ff;">覆盖率:</span>
            <span style="color: #52c41a; font-weight: bold; margin-left: 8px;">${networkData.metrics.coverage}%</span>
          </div>
          <div style="margin-bottom: 6px;">
            <span style="color: #91d5ff;">网速:</span>
            <span style="color: #faad14; font-weight: bold; margin-left: 8px;">${networkData.metrics.speed}Mbps</span>
          </div>
          <div>
            <span style="color: #91d5ff;">基站:</span>
            <span style="color: #722ed1; font-weight: bold; margin-left: 8px;">${networkData.metrics.baseStations}个</span>
          </div>
        </div>
      `;
    }
  },

  geo: {
    map: MAP_CONFIG.name,
    roam: true,
    zoom: MAP_CONFIG.zoom,
    center: [...MAP_CONFIG.center],
    aspectScale: 0.85,
    itemStyle: {
      areaColor: 'rgba(24, 144, 255, 0.05)',
      borderColor: '#1890ff',
      borderWidth: 1,
      shadowColor: 'rgba(24, 144, 255, 0.3)',
      shadowBlur: 10,
      shadowOffsetY: 3
    },
    emphasis: {
      itemStyle: {
        areaColor: 'rgba(24, 144, 255, 0.15)',
        borderColor: '#40a9ff',
        borderWidth: 2,
        shadowBlur: 15,
        shadowColor: 'rgba(64, 169, 255, 0.5)'
      }
    },
    label: {
      show: true,
      color: '#ffffff',
      fontSize: 12,
      fontWeight: 'bold',
      textShadowColor: 'rgba(0, 0, 0, 0.8)',
      textShadowBlur: 2
    }
  },

  series: [
    {
      name: '长沙网络分布',
      type: 'map',
      map: MAP_CONFIG.name,
      geoIndex: 0,
      data: districtData.value.map(item => ({
        name: item.name,
        value: item.networkScore,
        itemStyle: {
          areaColor: getStatusColor(item.status, 'area'),
          borderColor: getStatusColor(item.status, 'border'),
          borderWidth: 2
        }
      })),
      label: {
        show: true,
        color: '#ffffff',
        fontSize: 12,
        fontWeight: 'bold',
        textShadowColor: 'rgba(0, 0, 0, 0.8)',
        textShadowBlur: 2
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 14,
          color: '#40a9ff'
        }
      }
    },
    {
      name: '网络指标',
      type: 'scatter',
      coordinateSystem: 'geo',
      data: districtData.value.map(item => ({
        name: item.name,
        value: [...item.coordinates, item.networkScore, item.satisfaction]
      })),
      symbolSize: (val: number[]) => Math.max(val[2] / 5, 8),
      itemStyle: {
        color: (params: any) => {
          const district = districtData.value.find(d => d.name === params.name);
          return district ? getStatusColor(district.status, 'border') : '#1890ff';
        },
        shadowBlur: 10,
        shadowColor: 'rgba(24, 144, 255, 0.5)'
      },
      tooltip: {
        show: false
      }
    }
  ]
}));

// 处理地图点击事件
const handleMapClick = (district: string) => {
  selectedDistrict.value = selectedDistrict.value === district ? '' : district;
};

// UnoCSS 样式类映射 - 蓝色主题
const STATUS_CLASSES = markRaw({
  excellent: {
    border: 'border-green-500/30',
    bg: 'bg-green-400',
    text: 'text-green-400',
    leftBorder: 'border-l-4 border-l-green-400'
  },
  good: {
    border: 'border-blue-500/30',
    bg: 'bg-blue-400',
    text: 'text-blue-400',
    leftBorder: 'border-l-4 border-l-blue-400'
  },
  warning: {
    border: 'border-yellow-500/30',
    bg: 'bg-yellow-400',
    text: 'text-yellow-400',
    leftBorder: 'border-l-4 border-l-yellow-400'
  },
  poor: {
    border: 'border-red-500/30',
    bg: 'bg-red-400',
    text: 'text-red-400',
    leftBorder: 'border-l-4 border-l-red-400'
  }
} as const);

// 获取状态对应的样式类
const getStatusClass = (status: NetworkMetrics['status'], type: keyof typeof STATUS_CLASSES.excellent) => {
  return STATUS_CLASSES[status][type];
};
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    :card-type="props.cardType as any"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
    theme="blue"
  >
    <!-- 地图容器 - 使用相对定位和固定高度 -->
    <div class="relative h-full w-full overflow-hidden">
      <!-- 地图背景装饰 -->
      <div class="absolute inset-0 opacity-15">
        <!-- 科技网格 -->
        <div class="bg-grid-pattern absolute inset-0 opacity-30" />
        <!-- 边框装饰 - 蓝色主题 -->
        <div class="absolute left-0 top-0 h-full w-1px from-transparent via-blue-500 to-transparent bg-gradient-to-b" />
        <div
          class="absolute right-0 top-0 h-full w-1px from-transparent via-blue-500 to-transparent bg-gradient-to-b"
        />
        <div class="absolute left-0 top-0 h-1px w-full from-transparent via-blue-500 to-transparent bg-gradient-to-r" />
        <div
          class="absolute bottom-0 left-0 h-1px w-full from-transparent via-blue-500 to-transparent bg-gradient-to-r"
        />
      </div>

      <!-- 地图主体 - 固定高度 -->
      <div ref="domRef" class="absolute inset-0 z-10" />

      <!-- 地图加载状态指示器 -->
      <div v-if="!domRef" class="absolute inset-0 z-20 flex items-center justify-center">
        <div class="text-center">
          <div class="mb-4 text-4xl text-blue-400">🗺️</div>
          <div class="text-white/80">正在加载长沙地图...</div>
        </div>
      </div>

      <!-- 数据统计面板 -->
      <div class="absolute right-2 top-2 space-y-1 z-30">
        <div
          v-for="(item, index) in districtData.slice(0, 3)"
          :key="item.name"
          class="data-panel flex animate-fade-in-right items-center gap-2 rounded-4px px-2 py-1 text-xs"
          :style="{ animationDelay: `${index * 0.2}s` }"
          :class="getStatusClass(item.status, 'border')"
        >
          <div class="h-2 w-2 animate-pulse rounded-full" :class="getStatusClass(item.status, 'bg')" />
          <span class="text-white/80">{{ item.name }}</span>
          <span class="font-600" :class="getStatusClass(item.status, 'text')">
            {{ item.networkScore }}
          </span>
        </div>
      </div>

      <!-- 网络状态总览 -->
      <div class="absolute bottom-2 left-2 space-y-1 z-30">
        <!-- 图例说明 -->
        <div class="flex gap-2 text-xs">
          <div class="legend-item flex items-center gap-1">
            <div class="h-2 w-2 rounded-sm bg-green-500/70" />
            <span class="text-white/80">优秀</span>
          </div>
          <div class="legend-item flex items-center gap-1">
            <div class="h-2 w-2 rounded-sm bg-blue-500/70" />
            <span class="text-white/80">良好</span>
          </div>
          <div class="legend-item flex items-center gap-1">
            <div class="h-2 w-2 rounded-sm bg-yellow-500/70" />
            <span class="text-white/80">预警</span>
          </div>
          <div class="legend-item flex items-center gap-1">
            <div class="h-2 w-2 rounded-sm bg-red-500/70" />
            <span class="text-white/80">异常</span>
          </div>
        </div>

        <!-- 网络总览数据 -->
        <div class="data-panel rounded-4px px-2 py-1 text-xs">
          <div class="mb-1 text-blue-400 font-600">网络总览</div>
          <div class="grid grid-cols-2 gap-1 text-white/80">
            <div>
              基站:
              <span class="text-blue-300 font-500">
                {{ districtData.reduce((sum, d) => sum + d.metrics.baseStations, 0) }}
              </span>
            </div>
            <div>
              用户:
              <span class="text-blue-300 font-500">
                {{ Math.round(districtData.reduce((sum, d) => sum + d.metrics.users, 0)) }}万
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 自定义操作按钮 -->
    <template #actions>
      <NButton text class="text-20px text-blue-400 transition-all duration-200 hover:scale-110 hover:text-blue-300">
        <SvgIcon icon="mdi:map-marker-radius" />
      </NButton>
      <NButton text class="text-20px text-blue-400 transition-all duration-200 hover:scale-110 hover:text-blue-300">
        <SvgIcon icon="mdi:layers-triple" />
      </NButton>
    </template>

    <!-- 全屏内容 -->
    <template #fullscreen-content>
      <div class="h-full w-full p-6">
        <!-- 全屏标题和统计 -->
        <div class="mb-6 flex items-center justify-between">
          <h2 class="flex items-center gap-3 text-2xl text-white font-600">
            <div class="h-8 w-8 flex items-center justify-center rounded-full bg-blue-500/20">
              <SvgIcon icon="mdi:map" class="text-blue-400" />
            </div>
            长沙市移动网络分布
          </h2>
          <div class="flex gap-6 text-sm">
            <div class="border border-green-500/20 rounded-8px bg-green-500/10 p-3 text-center">
              <div class="text-lg text-green-400 font-600">{{ statistics.excellent }}</div>
              <div class="text-white/70">优秀区域</div>
            </div>
            <div class="border border-blue-500/20 rounded-8px bg-blue-500/10 p-3 text-center">
              <div class="text-lg text-blue-400 font-600">{{ statistics.good }}</div>
              <div class="text-white/70">良好区域</div>
            </div>
            <div class="border border-yellow-500/20 rounded-8px bg-yellow-500/10 p-3 text-center">
              <div class="text-lg text-yellow-400 font-600">{{ statistics.warning }}</div>
              <div class="text-white/70">预警区域</div>
            </div>
            <div class="border border-red-500/20 rounded-8px bg-red-500/10 p-3 text-center">
              <div class="text-lg text-red-400 font-600">{{ statistics.poor }}</div>
              <div class="text-white/70">异常区域</div>
            </div>
            <div class="border border-blue-600/20 rounded-8px bg-blue-600/10 p-3 text-center">
              <div class="text-lg text-blue-300 font-600">{{ statistics.avgNetworkScore }}</div>
              <div class="text-white/70">网络指数</div>
            </div>
          </div>
        </div>

        <!-- 全屏详细信息 - 不显示地图 -->
        <div class="h-full">
          <!-- 详细数据列表 -->
          <div class="grid grid-cols-3 h-full gap-6">
            <div
              v-for="district in districtData"
              :key="district.name"
              class="cursor-pointer border border-blue-500/30 rounded-12px bg-blue-900/20 p-6 transition-all hover:border-blue-400/50 hover:bg-blue-900/30"
              :class="[
                selectedDistrict === district.name ? 'border-blue-400 bg-blue-900/40' : '',
                getStatusClass(district.status, 'leftBorder')
              ]"
              @click="handleMapClick(district.name)"
            >
              <div class="mb-4 flex items-center justify-between">
                <h3 class="text-xl text-white font-600">{{ district.name }}</h3>
                <span class="text-2xl font-600" :class="getStatusClass(district.status, 'text')">
                  {{ district.networkScore }}
                </span>
              </div>
              <div class="text-sm text-white/70 space-y-3">
                <div class="flex justify-between">
                  <span>满意度:</span>
                  <span class="text-cyan-300">{{ district.satisfaction }}%</span>
                </div>
                <div class="flex justify-between">
                  <span>网络覆盖:</span>
                  <span class="text-green-300">{{ district.metrics.coverage }}%</span>
                </div>
                <div class="flex justify-between">
                  <span>网络速度:</span>
                  <span class="text-blue-300">{{ district.metrics.speed }}Mbps</span>
                </div>
                <div class="flex justify-between">
                  <span>网络延迟:</span>
                  <span class="text-yellow-300">{{ district.metrics.latency }}ms</span>
                </div>
                <div class="flex justify-between">
                  <span>基站数量:</span>
                  <span class="text-purple-300">{{ district.metrics.baseStations }}个</span>
                </div>
                <div class="flex justify-between">
                  <span>用户数量:</span>
                  <span class="text-red-300">{{ district.metrics.users }}万</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </DashboardCard>
</template>

<style scoped>
@keyframes fade-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 8px rgba(24, 144, 255, 0.4);
  }
  50% {
    box-shadow: 0 0 25px rgba(64, 169, 255, 0.8);
  }
}

@keyframes grid-move {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 20px 20px;
  }
}

.animate-fade-in-right {
  animation: fade-in-right 0.6s ease-out forwards;
}

.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(24, 144, 255, 0.08) 1px, transparent 1px),
    linear-gradient(90deg, rgba(24, 144, 255, 0.08) 1px, transparent 1px);
  background-size: 20px 20px;
  animation: grid-move 12s linear infinite;
}

/* 地图区域特殊效果 */
.map-container {
  position: relative;
}

.map-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, transparent 0%, rgba(13, 41, 84, 0.2) 100%);
  pointer-events: none;
  z-index: 1;
}

/* 数据面板样式 - 蓝色主题 */
.data-panel {
  backdrop-filter: blur(12px);
  border: 1px solid rgba(24, 144, 255, 0.25);
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.08) 0%, rgba(13, 41, 84, 0.6) 100%);
  transition: all 0.3s ease;
}

.data-panel:hover {
  animation: pulse-glow 2s ease-in-out infinite;
  border-color: rgba(64, 169, 255, 0.4);
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.12) 0%, rgba(13, 41, 84, 0.8) 100%);
}

/* 图例样式 */
.legend-item {
  transition: all 0.3s ease;
}

.legend-item:hover {
  transform: scale(1.05);
  filter: brightness(1.3);
}
</style>
