<script setup lang="ts">
import { type ECOption, useEcharts } from '@/hooks/common/echarts';

defineOptions({
  name: 'RightTopContainer'
});

interface Props {
  title: string;
  icon: string;
  cardType?: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'metric',
  animationDelay: 0
});

// 模拟数据 - 数据大屏风格
const chartData = {
  months: ['01月', '02月', '03月', '04月', '05月', '06月'],
  sales: [1280, 1850, 1650, 1420, 1380, 1720], // 销售额（柱状图）
  profit: [320, 480, 420, 280, 260, 380], // 利润（柱状图）
  growth: [12.5, 18.2, 15.8, 9.6, 8.4, 16.3] // 增长率（折线图）
};

// ECharts 配置
function createChartOptions(): ECOption {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#64b5f6',
          width: 1,
          opacity: 0.8
        },
        lineStyle: {
          color: '#64b5f6',
          width: 1,
          opacity: 0.8
        }
      },
      backgroundColor: 'rgba(15, 23, 42, 0.95)',
      borderColor: '#3b82f6',
      borderWidth: 1,
      textStyle: {
        color: '#e2e8f0',
        fontSize: 12
      },
      padding: [8, 12],
      extraCssText: 'border-radius: 6px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);'
    },
    legend: {
      data: ['销售额', '利润', '增长率'],
      textStyle: {
        color: '#e2e8f0',
        fontSize: 11
      },
      top: 8,
      left: 'center',
      itemWidth: 12,
      itemHeight: 8,
      itemGap: 16,
      icon: 'roundRect'
    },
    xAxis: [
      {
        type: 'category',
        data: chartData.months,
        axisPointer: {
          type: 'shadow',
          shadowStyle: {
            color: 'rgba(100, 181, 246, 0.1)'
          }
        },
        axisLabel: {
          color: '#94a3b8',
          fontSize: 10,
          margin: 8
        },
        axisLine: {
          lineStyle: {
            color: '#334155',
            width: 1
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '金额(万)',
        nameTextStyle: {
          color: '#94a3b8',
          fontSize: 10,
          padding: [0, 0, 0, 20]
        },
        min: 0,
        max: 2000,
        interval: 400,
        axisLabel: {
          formatter: '{value}',
          color: '#64748b',
          fontSize: 10,
          margin: 8
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(148, 163, 184, 0.15)',
            width: 1,
            type: 'dashed'
          }
        }
      },
      {
        type: 'value',
        name: '增长率(%)',
        nameTextStyle: {
          color: '#94a3b8',
          fontSize: 10,
          padding: [0, 20, 0, 0]
        },
        min: 0,
        max: 20,
        interval: 4,
        position: 'right',
        axisLabel: {
          formatter: '{value}',
          color: '#64748b',
          fontSize: 10,
          margin: 8
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '销售额',
        type: 'bar',
        stack: 'total',
        barWidth: '60%',
        tooltip: {
          valueFormatter(value) {
            return `${value}万`;
          }
        },
        data: chartData.sales,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#3b82f6' },
              { offset: 1, color: '#1e40af' }
            ]
          },
          borderRadius: [2, 2, 0, 0]
        },
        emphasis: {
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#60a5fa' },
                { offset: 1, color: '#3b82f6' }
              ]
            }
          }
        }
      },
      {
        name: '利润',
        type: 'bar',
        stack: 'total',
        tooltip: {
          valueFormatter(value) {
            return `${value}万`;
          }
        },
        data: chartData.profit,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#10b981' },
              { offset: 1, color: '#059669' }
            ]
          },
          borderRadius: [2, 2, 0, 0]
        },
        emphasis: {
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#34d399' },
                { offset: 1, color: '#10b981' }
              ]
            }
          }
        }
      },
      {
        name: '增长率',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        tooltip: {
          valueFormatter(value) {
            return `${value}%`;
          }
        },
        data: chartData.growth,
        lineStyle: {
          color: '#f59e0b',
          width: 3,
          shadowColor: 'rgba(245, 158, 11, 0.3)',
          shadowBlur: 8,
          shadowOffsetY: 2
        },
        itemStyle: {
          color: '#f59e0b',
          borderColor: '#ffffff',
          borderWidth: 2,
          shadowColor: 'rgba(245, 158, 11, 0.5)',
          shadowBlur: 6
        },
        symbol: 'circle',
        symbolSize: 8,
        emphasis: {
          itemStyle: {
            color: '#fbbf24',
            borderColor: '#ffffff',
            borderWidth: 3,
            shadowColor: 'rgba(251, 191, 36, 0.8)',
            shadowBlur: 10
          },
          lineStyle: {
            width: 4
          }
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(245, 158, 11, 0.3)' },
              { offset: 1, color: 'rgba(245, 158, 11, 0.05)' }
            ]
          }
        }
      }
    ],
    grid: {
      left: '5%', // 约占容器宽度的12%
      right: '8%', // 约占容器宽度的12%
      top: '60', // 约占容器高度的15%
      bottom: '10', // 约占容器高度的15%
      containLabel: true // 自动包含标签
    },
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut',
    animationDelay: (idx: number) => idx * 100
  };
}

// 使用 ECharts Hook - 普通模式
const { domRef } = useEcharts(createChartOptions);

// 使用 ECharts Hook - 全屏模式
const { domRef: fullscreenDomRef } = useEcharts(createChartOptions);
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    :card-type="props.cardType as any"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
    theme="blue"
  >
    <!-- ECharts 图表容器 -->
    <div class="relative h-full w-full overflow-hidden">
      <div ref="domRef" class="h-full min-h-0 w-full"></div>
    </div>

    <!-- 全屏内容 -->
    <template #fullscreen-content>
      <div class="relative h-full w-full overflow-hidden">
        <div class="absolute left-0 right-0 top-4 z-10 text-center">
          <div class="text-2xl text-white/90 font-semibold">{{ props.title }} - 数据分析</div>
        </div>
        <!-- 全屏模式下的图表容器 -->
        <div class="h-full w-full pt-16">
          <div ref="fullscreenDomRef" class="h-full min-h-0 w-full"></div>
        </div>
      </div>
    </template>
  </DashboardCard>
</template>
