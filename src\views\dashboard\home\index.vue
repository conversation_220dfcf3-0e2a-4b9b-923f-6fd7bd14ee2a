<script setup lang="ts">
import { ref } from 'vue';
import LeftTop from './modules/left-top.vue';
import LeftMiddle from './modules/left-middle.vue';
import LeftBottom from './modules/left-bottom.vue';
import CenterTop from './modules/center-top.vue';
import CenterBottom from './modules/center-bottom.vue';
import RightTop from './modules/right-top.vue';
import RightMiddle from './modules/right-middle.vue';
import RightBottom from './modules/right-bottom.vue';

defineOptions({
  name: 'DashboardHome'
});

interface ContainerItem {
  title: string;
  height: string;
  icon: string;
  cardType?: string;
  calculatedHeight?: string;
}

// 定义各区域容器高度配置
const leftContainers = ref<ContainerItem[]>([
  { title: '降本增效', height: '30%', icon: 'mdi:chart-line' },
  { title: '倒三角支撑', height: '30%', icon: 'mdi:chart-bar' },
  { title: '易访库用户跟进', height: '35%', icon: 'mdi:chart-pie' }
]);

const centerContainers = ref<ContainerItem[]>([
  { title: '长沙移动网络', height: '60%', icon: 'mdi:signal' },
  { title: '满意度', height: '36%', icon: 'mdi:table' }
]);

const rightContainers = ref<ContainerItem[]>([
  { title: '省重点工作', height: '30%', icon: 'mdi:gauge' },
  { title: '攻坚行动', height: '30%', icon: 'mdi:chart-scatter-plot' },
  { title: '督办', height: '35%', icon: 'mdi:chart-timeline-variant' }
]);

// 计算各容器的实际高度
const calculateHeight = (containers: ContainerItem[]) => {
  return containers.map(container => {
    return {
      ...container,
      calculatedHeight: container.height
    };
  });
};

const leftItems = calculateHeight(leftContainers.value);
const centerItems = calculateHeight(centerContainers.value);
const rightItems = calculateHeight(rightContainers.value);
</script>

<template>
  <div class="h-full w-full p-16px">
    <div class="grid grid-cols-12 h-full w-full gap-16px">
      <!-- 左侧三个容器 -->
      <div class="col-span-3 h-full flex flex-col gap-16px">
        <!-- 左上容器 -->
        <div class="h-[30%] animate-delay-100">
          <LeftTop :title="leftItems[0].title" :icon="leftItems[0].icon" :animation-delay="0.1" />
        </div>

        <!-- 左中容器 -->
        <div class="h-[30%] animate-delay-200">
          <LeftMiddle :title="leftItems[1].title" :icon="leftItems[1].icon" :animation-delay="0.2" />
        </div>

        <!-- 左下容器 -->
        <div class="h-[35%] animate-delay-300">
          <LeftBottom :title="leftItems[2].title" :icon="leftItems[2].icon" :animation-delay="0.3" />
        </div>
      </div>

      <!-- 中间两个容器 -->
      <div class="col-span-6 h-full flex flex-col gap-16px">
        <!-- 中间上部容器 -->
        <div class="h-[60%] animate-delay-300">
          <CenterTop :title="centerItems[0].title" :icon="centerItems[0].icon" :animation-delay="0.3" />
        </div>

        <!-- 中间下部容器 -->
        <div class="h-[36.7%] animate-delay-400">
          <CenterBottom :title="centerItems[1].title" :icon="centerItems[1].icon" :animation-delay="0.4" />
        </div>
      </div>

      <!-- 右侧三个容器 -->
      <div class="col-span-3 h-full flex flex-col gap-16px">
        <!-- 右上容器 -->
        <div class="h-[30%] animate-delay-500">
          <RightTop :title="rightItems[0].title" :icon="rightItems[0].icon" :animation-delay="0.5" />
        </div>

        <!-- 右中容器 -->
        <div class="h-[30%] animate-delay-600">
          <RightMiddle :title="rightItems[1].title" :icon="rightItems[1].icon" :animation-delay="0.6" />
        </div>

        <!-- 右下容器 -->
        <div class="h-[35%] animate-delay-700">
          <RightBottom :title="rightItems[2].title" :icon="rightItems[2].icon" :animation-delay="0.7" />
        </div>
      </div>
    </div>
  </div>
</template>
